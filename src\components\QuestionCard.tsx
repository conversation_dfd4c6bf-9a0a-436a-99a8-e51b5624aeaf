'use client';

import { Question } from '@/types';
import { useLanguage } from '@/contexts/LanguageContext';

interface QuestionCardProps {
  question: Question;
  selectedAnswer: number | null;
  onAnswerSelect: (answerIndex: number) => void;
  showResult: boolean;
}

export default function QuestionCard({
  question,
  selectedAnswer,
  onAnswerSelect,
  showResult
}: QuestionCardProps) {
  const { t } = useLanguage();

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'beginner': return 'bg-gradient-to-r from-emerald-100 to-green-100 text-emerald-800 border-emerald-200';
      case 'intermediate': return 'bg-gradient-to-r from-amber-100 to-orange-100 text-orange-800 border-orange-200';
      case 'advanced': return 'bg-gradient-to-r from-red-100 to-pink-100 text-red-800 border-red-200';
      default: return 'bg-gradient-to-r from-blue-100 to-indigo-100 text-blue-800 border-blue-200';
    }
  };

  const getLevelIcon = (level: string) => {
    switch (level) {
      case 'beginner': return '🌱';
      case 'intermediate': return '🌿';
      case 'advanced': return '🌳';
      default: return '📚';
    }
  };

  return (
    <div className="flex items-center justify-center p-3 sm:p-4 quiz-container">
      <div className="max-w-4xl w-full">
        <div className="angular-card-sharp sharp-shadow-lg modern-border overflow-hidden">
          {/* Header */}
          <div className="angular-gradient px-4 sm:px-6 py-3 border-b border-gray-200/50">
            <div className="flex items-center justify-between mb-2">
              <div className={`flex items-center space-x-2 px-3 py-1.5 clip-corner-sm text-xs font-bold border sharp-shadow ${getLevelColor(question.level)}`}>
                <span className="text-sm">{getLevelIcon(question.level)}</span>
                <span>{t(question.level)}</span>
              </div>
              <div className="flex items-center space-x-2 angular-card clip-corner-sm px-2 py-1.5 sharp-shadow">
                <div className="w-6 h-6 bg-gradient-to-br from-blue-500 to-purple-600 clip-corner-sm flex items-center justify-center">
                  <svg className="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <span className="text-xs font-semibold text-gray-700">{t('question')}</span>
              </div>
            </div>

            <h2 className="text-lg sm:text-xl font-bold text-gray-800 leading-tight">
              {question.question}
            </h2>
          </div>

          {/* Options */}
          <div className="p-4 sm:p-6">
            <div className="space-y-3">
              {question.options.map((option, index) => {
                let buttonClass = "group w-full p-3 sm:p-4 text-left clip-corner border-2 transition-all duration-300 focus:outline-none focus:ring-4 focus:ring-blue-300/50 transform hover:-translate-y-0.5 ";

                if (showResult) {
                  if (index === question.correctAnswer) {
                    buttonClass += "bg-gradient-to-r from-emerald-50 to-green-50 border-emerald-400 text-emerald-800 sharp-shadow-lg";
                  } else if (index === selectedAnswer && index !== question.correctAnswer) {
                    buttonClass += "bg-gradient-to-r from-red-50 to-pink-50 border-red-400 text-red-800 sharp-shadow-lg";
                  } else {
                    buttonClass += "bg-gray-50/50 border-gray-200 text-gray-600 opacity-60";
                  }
                } else {
                  if (selectedAnswer === index) {
                    buttonClass += "bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-400 text-blue-800 sharp-shadow-lg scale-105 neon-glow";
                  } else {
                    buttonClass += "angular-card border-gray-200 text-gray-700 hover:bg-gradient-to-r hover:from-gray-50 hover:to-blue-50 hover:border-blue-300 hover:sharp-shadow-lg";
                  }
                }

                return (
                  <button
                    key={index}
                    onClick={() => !showResult && onAnswerSelect(index)}
                    disabled={showResult}
                    className={buttonClass}

                  >
                    <div className="flex items-center space-x-3">
                      <div className="flex-shrink-0 w-8 h-8 bg-gradient-to-br from-white to-gray-100 border-2 border-current clip-corner-sm flex items-center justify-center font-bold text-sm sharp-shadow group-hover:scale-110 transition-transform duration-200">
                        {String.fromCharCode(65 + index)}
                      </div>
                      <span className="flex-1 text-sm sm:text-base leading-relaxed font-medium">
                        {option}
                      </span>
                      {selectedAnswer === index && !showResult && (
                        <div className="flex-shrink-0">
                          <div className="w-5 h-5 bg-blue-500 clip-corner-sm flex items-center justify-center sharp-shadow">
                            <svg className="w-2.5 h-2.5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={3} d="M5 13l4 4L19 7" />
                            </svg>
                          </div>
                        </div>
                      )}
                    </div>
                  </button>
                );
              })}
            </div>

            {/* Instructions */}
            <div className="mt-4 p-3 angular-gradient clip-corner border border-blue-200/50 sharp-shadow">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-indigo-600 clip-corner-sm flex items-center justify-center flex-shrink-0 sharp-shadow">
                  <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div className="flex-1">
                  <div className="text-xs font-bold text-blue-800 mb-1">
                    {selectedAnswer !== null ? `✨ ${t('processingAnswer')}` : `👆 ${t('chooseAnswer')}`}
                  </div>
                  <div className="text-xs text-blue-600">
                    {selectedAnswer !== null ? t('answerSelected') || 'Answer selected! Checking...' : t('selectAnswer') || 'Select the best answer from the options above'}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
